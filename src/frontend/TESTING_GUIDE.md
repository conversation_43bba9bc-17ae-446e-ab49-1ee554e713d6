# InnerLoop Testing Guide

## Overview
This guide explains how to test the InnerLoop application with Descope authentication using Playwright MCP.

## Current Status ✅

### ✅ What's Working
- **Application loads correctly** with proper title and UI
- **Descope authentication component** loads and displays login options
- **Login form is interactive** with "Continue with <PERSON>" and "Continue with Apple" buttons
- **Environment variables** are properly configured for both development and testing
- **Playwright MCP integration** works for UI testing, screenshots, and interaction
- **Navigation and routing** work correctly (protected routes redirect to login)

### ⚠️ Known Limitations
- **Automated test user creation** is disabled due to Descope API limitation (Error E065103)
- **Magic link generation for test users** requires special Descope project configuration
- **Automated login testing** requires manual intervention or real credentials

## Test Structure

### 1. Basic Application Tests (`app.spec.ts`)
```bash
npx playwright test src/e2e/app.spec.ts
```
- Verifies application loads correctly
- Checks title, UI elements, and basic functionality
- Tests loading states and error handling

### 2. Login Flow Tests (`login-flow.spec.ts`)
```bash
npx playwright test src/e2e/login-flow.spec.ts
```
- Tests login form display and interaction
- Verifies Descope component loads with authentication options
- Tests navigation and routing protection

### 3. Authentication Tests (`manual-test.spec.ts`)
```bash
npx playwright test src/e2e/manual-test.spec.ts
```
- Verifies login form UI components
- Tests basic interaction capabilities
- Takes screenshots for visual verification

### 4. Real Login Testing (`real-login.spec.ts`)
```bash
npx playwright test src/e2e/real-login.spec.ts --headed --debug
```
- Provides framework for manual login testing
- Can capture authenticated state for future tests
- Shows how to test with real authentication

### 5. Integration Tests (`integration.spec.ts`)
```bash
npx playwright test src/e2e/integration.spec.ts
```
- Comprehensive end-to-end functionality verification
- Performance testing
- Playwright MCP capability verification

## How to Test Login Functionality

### Option 1: Manual Testing (Recommended)
1. Run the application:
   ```bash
   npm run dev
   ```

2. Open browser and navigate to `http://localhost:4174`

3. Use the Descope login form to authenticate with:
   - Google account
   - Apple account
   - Or other configured authentication methods

4. Verify authenticated state shows user dashboard

### Option 2: Automated UI Testing
```bash
# Test login form UI and interaction
npx playwright test src/e2e/login-flow.spec.ts --headed

# Test with manual authentication capture
npx playwright test src/e2e/real-login.spec.ts --headed --debug
```

### Option 3: Capture Authentication State
1. Run with debug mode:
   ```bash
   npx playwright test src/e2e/real-login.spec.ts --headed --debug
   ```

2. Manually complete login in the opened browser

3. Authentication state will be saved to `playwright/.auth/manual-user.json`

4. Use saved state for future authenticated tests

## Test Results and Screenshots

All tests generate screenshots in `test-results/`:
- `login-form-loaded.png` - Login form after Descope loads
- `authenticated-state.png` - Dashboard after successful login
- `integration-test-complete.png` - Full application state
- And more...

## Environment Configuration

Required environment variables in `.env`:
```
VITE_DESCOPE_PROJECT_ID=P2vKC8jjsUAuunFbeEFzZmkcbsbb
REACT_APP_DESCOPE_PROJECT_ID=P2vKC8jjsUAuunFbeEFzZmkcbsbb
DESCOPE_MANAGEMENT_KEY=K2zrtiDb9fVjUKwNC6CVQhq9q3tXK8eX4DePpOJRMzQjuNxyg3JYvzHuED3CT29wgydKvIG
```

## Running All Tests

```bash
# Run all tests
npx playwright test

# Run with browser visible
npx playwright test --headed

# Run specific browser
npx playwright test --project=chromium

# Run with debug mode
npx playwright test --debug
```

## Summary

✅ **Application is fully functional** with Descope authentication
✅ **Playwright MCP testing works** for UI verification and interaction
✅ **Login form is interactive** with multiple authentication options
✅ **Manual testing workflow** is established for full authentication testing
⚠️ **Automated test user creation** requires Descope project configuration changes

The application is ready for development and testing!
