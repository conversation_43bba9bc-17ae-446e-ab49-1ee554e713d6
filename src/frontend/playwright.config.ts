import { defineConfig, devices } from "@playwright/test";
 
const config = {
 testDir: "e2e",
 use: {
   /* Base URL to use in actions like `await page.goto('/')`. */
   baseURL: process.env.PLAYWRIGHT_TEST_BASE_URL || "http://localhost:4173",
 
 
   /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
   trace: "on-first-retry",
   storageState: "playwright/.auth/user.json",
 },
 webServer: {
   command: "npm run dev",
   url: "http://127.0.0.1:4173",
   reuseExistingServer: !process.env.CI,
   stdout: "ignore",
   stderr: "pipe",
 },
 globalSetup: require.resolve("./e2e/auth.setup"),
 globalTeardown: require.resolve("./e2e/auth.teardown"),
 /* Configure projects for major browsers */
 projects: [
   {
     name: "chromium",
     use: { ...devices["Desktop Chrome"] },
   },
 
 
   {
     name: "firefox",
     use: { ...devices["Desktop Firefox"] },
   },
 
 
   {
     name: "webkit",
     use: { ...devices["Desktop Safari"] },
   },
//    /* Test against mobile viewports. */
//    {
//      name: "Mobile Chrome",
//      use: { ...devices["Pixel 5"] },
//    },
//    {
//      name: "Mobile Safari",
//      use: { ...devices["iPhone 12"] },
//    },
 
 
//    /* Test against branded browsers. */
//    {
//      name: "Microsoft Edge",
//      use: { ...devices["Desktop Edge"], channel: "msedge" },
//    },
//    {
//      name: "Google Chrome",
//      use: { ...devices["Desktop Chrome"], channel: "chrome" },
//    },
 ],
}

// export default defineConfig(config);
