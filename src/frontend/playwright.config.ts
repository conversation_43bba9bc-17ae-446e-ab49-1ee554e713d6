import { defineConfig, devices } from "@playwright/test";

export default defineConfig({
 testDir: "src/e2e",
 use: {
   /* Base URL to use in actions like `await page.goto('/')`. */
   baseURL: process.env.PLAYWRIGHT_TEST_BASE_URL || "http://localhost:4174",


   /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
   trace: "on-first-retry",
   // Temporarily disable auth for basic testing
   // storageState: "playwright/.auth/user.json",
 },
 webServer: {
   command: "npm run dev",
   url: "http://localhost:4174",
   reuseExistingServer: !process.env.CI,
   stdout: "ignore",
   stderr: "pipe",
 },
 // Temporarily disable auth setup for basic testing
 // globalSetup: "./src/e2e/auth.setup.ts",
 // globalTeardown: "./src/e2e/auth.teardown.ts",
 /* Configure projects for major browsers */
 projects: [
   {
     name: "chromium",
     use: { ...devices["Desktop Chrome"] },
   },
 
 
   {
     name: "firefox",
     use: { ...devices["Desktop Firefox"] },
   },
 
 
   {
     name: "webkit",
     use: { ...devices["Desktop Safari"] },
   },
//    /* Test against mobile viewports. */
//    {
//      name: "Mobile Chrome",
//      use: { ...devices["Pixel 5"] },
//    },
//    {
//      name: "Mobile Safari",
//      use: { ...devices["iPhone 12"] },
//    },
 
 
//    /* Test against branded browsers. */
//    {
//      name: "Microsoft Edge",
//      use: { ...devices["Desktop Edge"], channel: "msedge" },
//    },
//    {
//      name: "Google Chrome",
//      use: { ...devices["Desktop Chrome"], channel: "chrome" },
//    },
 ],
});
