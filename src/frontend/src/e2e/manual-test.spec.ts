import { test, expect } from '@playwright/test';

test.describe('Authentication Tests', () => {
  test('should show login form and allow interaction', async ({ page }) => {
    console.log('Testing login form interaction...');

    // Navigate to the app
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Wait for Descope to load
    await page.waitForTimeout(3000);

    // Take screenshot of login page
    await page.screenshot({
      path: 'test-results/login-page.png',
      fullPage: true
    });

    // Verify we see the login form
    await expect(page.locator('text=Please sign in to continue')).toBeVisible();
    await expect(page.locator('descope-wc')).toBeVisible({ timeout: 10000 });

    // Try to interact with the Descope component
    const descopeComponent = page.locator('descope-wc');
    await expect(descopeComponent).toBeVisible();

    // Wait for the Descope component to fully load
    await page.waitForTimeout(2000);

    // Check if there are any input fields within the Descope component
    const anyInput = page.locator('input');

    // Take another screenshot after component loads
    await page.screenshot({
      path: 'test-results/login-form-loaded.png',
      fullPage: true
    });

    // Check if we can find any input fields
    const inputCount = await anyInput.count();
    console.log(`Found ${inputCount} input fields`);

    if (inputCount > 0) {
      console.log('✅ Login form has input fields - interaction possible');

      // Try to focus on the first input field
      await anyInput.first().focus();
      console.log('✅ Successfully focused on input field');
    } else {
      console.log('ℹ️ No input fields found - Descope component may still be loading');
    }

    // Verify the page structure is correct
    await expect(page).toHaveTitle('InnerLoop');
    console.log('✅ Login form test completed successfully!');
  });

  test('should show login form when not authenticated', async ({ page }) => {
    await page.goto('/');

    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');

    // Wait a bit more for Descope to load
    await page.waitForTimeout(3000);

    // Take a full page screenshot
    await page.screenshot({
      path: 'test-results/unauthenticated-state.png',
      fullPage: true
    });

    // Verify the page title
    await expect(page).toHaveTitle('InnerLoop');

    // Verify login form is shown
    await expect(page.locator('text=Welcome to InnerLoop')).toBeVisible();
    await expect(page.locator('text=Please sign in to continue')).toBeVisible();
    await expect(page.locator('descope-wc')).toBeVisible({ timeout: 10000 });

    console.log('✅ Unauthenticated state verified successfully!');
  });
});
