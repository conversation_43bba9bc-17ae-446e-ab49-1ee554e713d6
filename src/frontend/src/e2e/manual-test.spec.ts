import { test, expect } from '@playwright/test';

test('Manual verification - take screenshot of the app', async ({ page }) => {
  await page.goto('/');
  
  // Wait for the page to load completely
  await page.waitForLoadState('networkidle');
  
  // Wait a bit more for <PERSON><PERSON> to load
  await page.waitForTimeout(3000);
  
  // Take a full page screenshot
  await page.screenshot({ 
    path: 'test-results/app-manual-verification.png',
    fullPage: true 
  });
  
  // Verify the page title
  await expect(page).toHaveTitle('InnerLoop');
  
  console.log('Screenshot saved to test-results/app-manual-verification.png');
});
