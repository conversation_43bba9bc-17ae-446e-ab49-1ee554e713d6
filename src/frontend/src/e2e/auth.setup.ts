import Descope from "@descope/node-sdk";
import { chromium, type FullConfig } from "@playwright/test";
import * as crypto from "crypto";
 
 
import dotenv from "dotenv";
dotenv.config();
 
 
export const authFile = "playwright/.auth/user.json";
 
 
async function globalSetup(config: FullConfig) {
 const browser = await chromium.launch();
 const page = await browser.newPage();


 const testUser = crypto.randomBytes(20).toString("hex");
 process.env.TEST_USER = testUser;


 const descope = Descope({
   projectId: process.env.REACT_APP_DESCOPE_PROJECT_ID!,
   managementKey: process.env.DESCOPE_MANAGEMENT_KEY!,
 });


 try {
   // Create test user
   console.log('Creating test user:', testUser);
   await descope.management.user.createTestUser(testUser, "<EMAIL>");

   // Generate magic link for test user
   console.log('Generating magic link for test user');
   const magiclink = await descope.management.user.generateMagicLinkForTestUser(
     "email",
     testUser,
     config.projects[0].use.baseURL || "http://localhost:4174"
   );

   if (!magiclink || !magiclink.data || !magiclink.data.link) {
     throw new Error('Failed to generate magic link - no link returned');
   }

   console.log('Magic link generated:', magiclink.data.link);
   const token = magiclink.data.link.split("?t=")[1];

   if (!token) {
     throw new Error('Failed to extract token from magic link');
   }

   // Verify the magic link token
   console.log('Verifying magic link token');
   const auth = await descope.magicLink.verify(token);

   if (!auth || !auth.data || !auth.data.sessionJwt) {
     throw new Error('Failed to verify magic link - no session token returned');
   }

   // Navigate to the app and set tokens in localStorage
   await page.goto(config.projects[0].use.baseURL!);
   await page.evaluate(
     ([ds, dsr]) => {
       window.localStorage.setItem("DS", ds);
       if (dsr) {
         window.localStorage.setItem("DSR", dsr);
       }
     },
     [auth.data.sessionJwt, auth.data.refreshJwt || ""]
   );

   console.log('Tokens set in localStorage, saving storage state');
   await page.context().storageState({ path: authFile });
   console.log('Auth setup completed successfully');

 } catch (error) {
   console.error('Auth setup failed:', error);
   // Don't throw the error, just log it so tests can run without auth
   // throw error;
 } finally {
   await browser.close();
 }
}

export default globalSetup;