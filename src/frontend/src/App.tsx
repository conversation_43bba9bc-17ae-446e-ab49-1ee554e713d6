import { useSession, useUser, useDescope } from '@descope/react-sdk';
import { Descope } from '@descope/react-sdk';

/**
 * Main App component that handles authentication state
 */
const App = () => {
  const { isAuthenticated, isSessionLoading } = useSession();
  const { user, isUserLoading } = useUser();
  const { logout } = useDescope();

  console.log('App component rendering...', { isAuthenticated, isSessionLoading, isUserLoading, user });

  const handleLogout = () => {
    logout();
  };

  // Show loading spinner while checking authentication
  if (isSessionLoading || isUserLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f9fafb',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '4px solid #e5e7eb',
            borderTop: '4px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#6b7280' }}>Loading...</p>
        </div>
      </div>
    );
  }

  // Show Descope login if not authenticated
  if (!isAuthenticated) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f9fafb',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ maxWidth: '400px', width: '100%', padding: '32px' }}>
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <div style={{
              width: '48px',
              height: '48px',
              backgroundColor: '#ddd6fe',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px',
              fontSize: '20px',
              fontWeight: 'bold',
              color: '#5b21b6'
            }}>
              IL
            </div>
            <h2 style={{
              fontSize: '1.875rem',
              fontWeight: 'bold',
              color: '#111827',
              marginBottom: '8px'
            }}>
              Welcome to InnerLoop
            </h2>
            <p style={{ color: '#6b7280', fontSize: '14px' }}>
              Please sign in to continue
            </p>
          </div>
          <Descope
            flowId="sign-up-or-in"
            onSuccess={(e) => {
              console.log('Login successful:', e.detail.user);
            }}
            onError={(e) => {
              console.error('Login error:', e);
            }}
          />
        </div>
      </div>
    );
  }

  // User is authenticated - show simple dashboard
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f9fafb',
      padding: '24px'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '24px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <h1 style={{
            fontSize: '2rem',
            fontWeight: 'bold',
            color: '#111827',
            marginBottom: '16px'
          }}>
            Welcome, {user?.name || 'User'}!
          </h1>
          <p style={{ color: '#6b7280', marginBottom: '24px' }}>
            You are successfully authenticated with InnerLoop.
          </p>
          <div style={{
            padding: '16px',
            backgroundColor: '#f3f4f6',
            borderRadius: '6px',
            fontSize: '14px',
            color: '#374151'
          }}>
            <strong>User Info:</strong><br />
            Email: {user?.email || 'Not provided'}<br />
            User ID: {user?.userId || 'Not provided'}
          </div>
          <button
            onClick={handleLogout}
            style={{
              marginTop: '24px',
              padding: '12px 24px',
              backgroundColor: '#dc2626',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.backgroundColor = '#b91c1c';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.backgroundColor = '#dc2626';
            }}
          >
            Sign Out
          </button>
        </div>
      </div>
    </div>
  );
};

export default App;